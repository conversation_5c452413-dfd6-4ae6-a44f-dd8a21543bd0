package fr.enedis.i2r.si.mock;

import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.networknt.schema.InputFormat;
import com.networknt.schema.JsonSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.SchemaLocation;
import com.networknt.schema.SpecVersion.VersionFlag;
import com.networknt.schema.ValidationMessage;

public class ConfigurationValidator {

    private static final Logger logger = LoggerFactory.getLogger(ConfigurationValidator.class);

    public static boolean validateConfigurationBoitier(String jsonConfig) {
        JsonSchemaFactory factory = JsonSchemaFactory.getInstance(VersionFlag.V202012);
        JsonSchema schemaFromClasspath = factory.getSchema(SchemaLocation.of("classpath:schema/icom-config-json-schema.json"));

        Set<ValidationMessage> errors = schemaFromClasspath.validate(jsonConfig, InputFormat.JSON);

        if (errors.size() > 0) {
            logger.error("Erreur de validation du schema json {}", errors);
            // TODO: Print errors
            return false;
        }

        return true;
    }

    public static boolean validateStatsBoitier(String jsonStats) {
        JsonSchemaFactory factory = JsonSchemaFactory.getInstance(VersionFlag.V4);
        JsonSchema schemaFromClasspath = factory.getSchema(SchemaLocation.of("classpath:schema/icom-stats-json-schema.json"));

        Set<ValidationMessage> errors = schemaFromClasspath.validate(jsonStats, InputFormat.JSON);

        if (errors.size() > 0) {
            logger.error("Erreur de validation du schema json stats. Nombre d'erreurs: {}", errors.size());
            for (ValidationMessage error : errors) {
                logger.error("Erreur de validation: {}", error.getMessage());
            }
            return false;
        }

        return true;
    }
}
